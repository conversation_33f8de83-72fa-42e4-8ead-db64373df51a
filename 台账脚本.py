import os
import pandas as pd
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path
import re

class FileNameParser:
    @staticmethod
    def parse_filename(filename):
        """解析文件名，返回日期、时长和描述"""
        pattern = r"(\d{8})_(\d+)_(.*)"
        match = re.match(pattern, filename)
        if match:
            date_str, duration, description = match.groups()
            try:
                date = datetime.strptime(date_str, '%Y%m%d').strftime('%Y-%m-%d')
                return {
                    'date': date,
                    'duration': int(duration),
                    'description': description
                }
            except ValueError:
                return None
        return None

class FileTracker:
    def __init__(self, root_path, year):
        self.root_path = root_path
        self.year = year
        self.excel_name = f"{year}年党建工作台账.xlsx"
        self.allowed_extensions = ('.doc', '.docx', '.pdf', '.xlsx', '.xls', '.ppt', '.pptx')
        self.training_categories = ['党建学习材料', '安全学习材料', '三会一课']

    def scan_files(self):
        """扫描文件并收集信息"""
        file_info = []
        total_duration = {}  # 用于统计各类别总时长
        category_count = {}  # 用于统计各类别的培训次数
        
        for root, dirs, files in os.walk(self.root_path):
            rel_path = os.path.relpath(root, self.root_path)
            path_parts = Path(rel_path).parts
            
            category = path_parts[1] if len(path_parts) >= 2 else ''
            
            for file in files:
                if file.lower().endswith(self.allowed_extensions):
                    file_name = os.path.splitext(file)[0]
                    full_path = os.path.join(root, file)
                    mod_time = datetime.fromtimestamp(os.path.getmtime(full_path))
                    
                    # 解析文件名
                    parsed_info = FileNameParser.parse_filename(file_name)
                    
                    if parsed_info and category in self.training_categories:
                        # 累计培训时长
                        total_duration[category] = total_duration.get(category, 0) + parsed_info['duration']
                        # 累计培训次数
                        category_count[category] = category_count.get(category, 0) + 1
                        
                        file_info.append({
                            '文件名': parsed_info['description'],
                            '文件类别': category,
                            '培训日期': parsed_info['date'],
                            '培训时长(分钟)': parsed_info['duration'],
                            '文件链接': full_path,
                            '最后修改时间': mod_time.strftime('%Y-%m-%d %H:%M:%S')
                        })
                    else:
                        file_info.append({
                            '文件名': file_name,
                            '文件类别': category,
                            '培训日期': '',
                            '培训时长(分钟)': 0,
                            '文件链接': full_path,
                            '最后修改时间': mod_time.strftime('%Y-%m-%d %H:%M:%S')
                        })

        df = pd.DataFrame(file_info)
        if not df.empty:
            df.insert(0, '序号', range(1, len(df) + 1))
        
        return df, total_duration, category_count

    def update_tracker(self):
        """更新台账"""
        new_df, total_duration, category_count = self.scan_files()
        
        with pd.ExcelWriter(self.excel_name, engine='xlsxwriter') as writer:
            # 将日期列转换为datetime类型
            new_df['培训日期'] = pd.to_datetime(new_df['培训日期'], errors='coerce')
            
            new_df.to_excel(writer, index=False, sheet_name='文件台账')
            
            # 创建统计表
            stats_data = []
            for category in self.training_categories:
                if category in total_duration:
                    stats_data.append({
                        '类别': category,
                        '总时长(小时)': round(total_duration[category] / 60, 1),
                        '培训次数': category_count[category]
                    })
                else:
                    stats_data.append({
                        '类别': category,
                        '总时长(小时)': 0,
                        '培训次数': 0
                    })
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, index=False, sheet_name='培训统计')
            
            # 获取工作簿和工作表对象
            workbook = writer.book
            worksheet = writer.sheets['文件台账']
            stats_worksheet = writer.sheets['培训统计']
            
            # 定义格式
            header_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            link_format = workbook.add_format({
                'font_color': 'blue',
                'underline': True
            })
            
            cell_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            date_format = workbook.add_format({
                'align': 'center',
                'valign': 'vcenter',
                'border': 1,
                'num_format': 'yyyy-mm-dd'
            })
            
            # 设置列宽
            worksheet.set_column('A:A', 8)   # 序号
            worksheet.set_column('B:B', 40)  # 文件名
            worksheet.set_column('C:C', 20)  # 文件类别
            worksheet.set_column('D:D', 15)  # 培训日期
            worksheet.set_column('E:E', 15)  # 培训时长
            worksheet.set_column('F:F', 50)  # 文件链接
            worksheet.set_column('G:G', 20)  # 最后修改时间
            
            # 设置表头
            for col_num, value in enumerate(new_df.columns.values):
                worksheet.write(0, col_num, value, header_format)
            
            # 写入数据和超链接
            for row_num, row in enumerate(new_df.values, start=1):
                for col_num, value in enumerate(row):
                    if col_num == 5:  # 文件链接列
                        abs_path = os.path.abspath(value)
                        formatted_path = abs_path.replace("\\", "/")
                        url = f'file:///{formatted_path}'
                        worksheet.write_url(row_num, col_num, url, link_format, value)
                    elif col_num == 3:  # 培训日期列
                        if pd.notnull(value):
                            worksheet.write_datetime(row_num, col_num, value, date_format)
                        else:
                            worksheet.write(row_num, col_num, '', cell_format)
                    else:
                        worksheet.write(row_num, col_num, value, cell_format)
            
            # 添加筛选器
            worksheet.autofilter(0, 0, len(new_df), len(new_df.columns) - 1)
            
            # 设置统计表格式
            stats_worksheet.set_column('A:A', 20)  # 类别列宽
            stats_worksheet.set_column('B:C', 15)  # 其他列宽
            
            # 设置统计表表头
            for col_num, value in enumerate(stats_df.columns.values):
                stats_worksheet.write(0, col_num, value, header_format)
            
            # 写入统计数据
            for row_num, row in enumerate(stats_df.values, start=1):
                for col_num, value in enumerate(row):
                    stats_worksheet.write(row_num, col_num, value, cell_format)

        return len(new_df), total_duration, category_count

class TrackerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("党建文件台账管理系统")
        self.root.geometry("450x550")
        self.setup_ui()

    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, 
                              text="党建文件台账管理系统", 
                              font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=20)

        # 年份选择
        year_frame = ttk.Frame(main_frame)
        year_frame.grid(row=1, column=0, columnspan=2, pady=10)
        
        year_label = ttk.Label(year_frame, text="选择年份：")
        year_label.pack(side=tk.LEFT)
        
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        year_entry = ttk.Entry(year_frame, textvariable=self.year_var, width=10)
        year_entry.pack(side=tk.LEFT, padx=5)

        # 生成按钮
        generate_button = ttk.Button(main_frame, 
                                   text="生成台账", 
                                   command=self.generate_tracker)
        generate_button.grid(row=2, column=0, columnspan=2, pady=20)

        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=3, column=0, columnspan=2, pady=10)

        # 文件命名规范说明
        naming_frame = ttk.LabelFrame(main_frame, text="文件命名规范", padding="10")
        naming_frame.grid(row=4, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))

        naming_text = """1. 格式：学习日期_学习时长_文件名
2. 示例：20240311_120_2024第三季度党课.docx
3. 日期格式：YYYYMMDD
4. 时长单位：分钟"""
        
        naming_label = ttk.Label(naming_frame, text=naming_text, justify=tk.LEFT)
        naming_label.pack(anchor=tk.W)

        # 支持的文件类型说明
        file_types_frame = ttk.LabelFrame(main_frame, text="支持的文件类型", padding="10")
        file_types_frame.grid(row=5, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))

        file_types_text = "支持的文件类型：.doc, .docx, .pdf, .xlsx, .xls, .ppt, .pptx"
        file_types_label = ttk.Label(file_types_frame, text=file_types_text, justify=tk.LEFT)
        file_types_label.pack(anchor=tk.W)

    def generate_tracker(self):
        year = self.year_var.get()
        
        # 验证年份输入
        if not year.isdigit() or len(year) != 4:
            messagebox.showerror("错误", "请输入有效的四位数年份")
            return

        self.status_var.set("正在处理...")
        self.root.update()

        try:
            tracker = FileTracker(".", year)
            file_count, total_duration, category_count = tracker.update_tracker()
            
            # 显示详细统计信息
            stats_msg = f"台账已更新，共记录{file_count}个文件\n\n培训统计："
            for category in tracker.training_categories:
                if category in total_duration:
                    duration_hours = round(total_duration[category] / 60, 1)
                    count = category_count[category]
                    stats_msg += f"\n{category}: {duration_hours}小时 ({count}次)"
                else:
                    stats_msg += f"\n{category}: 0小时 (0次)"
            
            messagebox.showinfo("成功", stats_msg)
            self.status_var.set(f"已完成 - 记录{file_count}个文件")
        except Exception as e:
            messagebox.showerror("错误", f"处理过程中出现错误：{str(e)}")
            self.status_var.set("处理失败")

    def run(self):
        self.root.mainloop()

# 启动应用
if __name__ == "__main__":
    app = TrackerGUI()
    app.run()
