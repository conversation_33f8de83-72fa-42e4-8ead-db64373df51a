import os
import datetime
import pandas as pd
import re
import docx
import PyPDF2
import tkinter as tk
import win32com.client
from tkinter import filedialog, messagebox
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment
from pptx import Presentation
from pathlib import Path
import shutil

class FileInventoryApp:
    def __init__(self, root):
        self.root = root
        self.root.title("文件台账生成工具")
        self.root.geometry("600x400")
        self.root.resizable(True, True)
        
        # 设置界面样式
        self.root.configure(bg="#f0f0f0")
        
        # 创建主框架
        main_frame = tk.Frame(root, bg="#f0f0f0")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(
            main_frame, 
            text="党建安全工作台账生成工具", 
            font=("Arial", 16, "bold"),
            bg="#f0f0f0"
        )
        title_label.pack(pady=10)
        
        # 文件夹路径选择
        path_frame = tk.Frame(main_frame, bg="#f0f0f0")
        path_frame.pack(fill=tk.X, pady=10)
        
        path_label = tk.Label(
            path_frame, 
            text="年度安全工作文件夹:", 
            width=15,
            anchor="w",
            bg="#f0f0f0"
        )
        path_label.pack(side=tk.LEFT, padx=5)
        
        self.path_var = tk.StringVar()
        path_entry = tk.Entry(path_frame, textvariable=self.path_var, width=50)
        path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        browse_button = tk.Button(
            path_frame, 
            text="浏览...", 
            command=self.browse_folder,
            width=8,
            bg="#dcdcdc"
        )
        browse_button.pack(side=tk.LEFT, padx=5)
        
        # 备份选项
        backup_frame = tk.Frame(main_frame, bg="#f0f0f0")
        backup_frame.pack(fill=tk.X, pady=5)
        
        self.backup_var = tk.BooleanVar(value=True)
        backup_check = tk.Checkbutton(
            backup_frame,
            text="更新现有台账时创建备份",
            variable=self.backup_var,
            bg="#f0f0f0"
        )
        backup_check.pack(side=tk.LEFT, padx=20)
        
        # 状态显示
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪")
        
        status_frame = tk.Frame(main_frame, bg="#f0f0f0")
        status_frame.pack(fill=tk.X, pady=10)
        
        status_label = tk.Label(
            status_frame, 
            text="状态:", 
            width=15,
            anchor="w",
            bg="#f0f0f0"
        )
        status_label.pack(side=tk.LEFT, padx=5)
        
        status_value = tk.Label(
            status_frame, 
            textvariable=self.status_var,
            bg="#f0f0f0",
            anchor="w"
        )
        status_value.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 生成按钮
        button_frame = tk.Frame(main_frame, bg="#f0f0f0")
        button_frame.pack(pady=15)
        
        generate_button = tk.Button(
            button_frame, 
            text="生成台账", 
            command=self.generate_inventory,
            width=15,
            height=2,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 10, "bold")
        )
        generate_button.pack(side=tk.LEFT, padx=10)
        
        exit_button = tk.Button(
            button_frame, 
            text="退出", 
            command=root.quit,
            width=15,
            height=2,
            bg="#f44336",
            fg="white",
            font=("Arial", 10, "bold")
        )
        exit_button.pack(side=tk.LEFT, padx=10)
        
        # 帮助信息
        help_text = """使用说明：
1. 点击"浏览..."选择对应的"XXXX年安全工作"文件夹
2. 点击"生成台账"按钮
3. 台账将保存在所选文件夹下
4. 如果已有台账存在，将会更新并提示变更情况"""
        
        help_label = tk.Label(
            main_frame, 
            text=help_text,
            justify=tk.LEFT,
            bg="#f0f0f0",
            fg="#555555",
            font=("Arial", 9)
        )
        help_label.pack(anchor="w", pady=10)
        
    def browse_folder(self):
        """打开文件夹选择对话框"""
        folder_path = filedialog.askdirectory(title="请选择年度安全工作文件夹")
        if folder_path:
            self.path_var.set(folder_path)
            # 检查是否已有台账
            self.check_existing_inventory(folder_path)

    def adjust_column_widths(self,excel_path, special_column='B', enable_text_wrap=True):
        """
        调整Excel文件的列宽
        
        参数:
            excel_path: Excel文件路径
            special_column: 需要根据全部内容调整宽度的特殊列（默认为'B'）
            enable_text_wrap: 是否为特殊列启用自动换行（默认为True）
        
        返回:
            成功返回True，失败返回False
        """
        try:
            # 加载工作簿
            wb = load_workbook(excel_path)
            ws = wb.active
            
            # 设置最小宽度和最大宽度限制
            min_width = 8
            max_width = 80
            
            # 遍历每一列
            for column in ws.columns:
                column_letter = get_column_letter(column[0].column)
                
                # 特殊列处理 - 根据所有内容自动调整宽度
                if column_letter == special_column:
                    max_length = 0
                    for cell in column:
                        try:
                            if cell.value:
                                # 对中文等宽字符进行特殊处理
                                text = str(cell.value)
                                # 中文字符计为1.7个单位宽度
                                cell_length = sum(1.7 if ord(char) > 127 else 1 for char in text)
                                if cell_length > max_length:
                                    max_length = cell_length
                        except:
                            pass
                            
                    # 为特殊列设置自动换行
                    if enable_text_wrap:
                        for row_idx in range(1, ws.max_row + 1):
                            cell = ws.cell(row=row_idx, column=ord(special_column) - 64)  # 将列字母转换为列索引
                            cell.alignment = Alignment(wrap_text=True)
                # 其他列只根据标题行调整宽度
                else:
                    # 只取第一行(标题行)的内容
                    header_cell = column[0]
                    try:
                        if header_cell.value:
                            # 对中文等宽字符进行特殊处理
                            text = str(header_cell.value)
                            # 中文字符计为1.7个单位宽度
                            max_length = sum(1.7 if ord(char) > 127 else 1 for char in text)
                        else:
                            max_length = 0
                    except:
                        max_length = 0
                
                # 设置列宽（加一些额外空间以便更好地显示）
                adjusted_width = max(min(max_length + 4, max_width), min_width)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 为特殊列增加额外宽度，因为它包含长文本
            if special_column in ws.column_dimensions:
                current_width = ws.column_dimensions[special_column].width
                ws.column_dimensions[special_column].width = min(current_width * 1.2, max_width)
            
            # 保存调整后的工作簿
            wb.save(excel_path)
            return True
        except Exception as e:
            print(f"调整列宽时出错: {str(e)}")
            return False

    def get_training_duration(self,file_path, file_name):
        """根据文件类型和页数确定培训时长"""
        
        # 检查是否为党建学习材料
        if "党建" in file_path or "党建" in file_name:
            return 60  # 党建学习固定60分钟
        
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        try:
            # PPT文件
            if ext in ['.pptx', '.ppt']:
                if ext == '.pptx':  # 只支持.pptx格式
                    ppt = Presentation(file_path)
                    pages = len(ppt.slides)
                    return round(max(0.2, pages * 0.2), 2)  # 至少10秒(1/6分钟)，并保留2位小数
                else:
                    print(f"警告: 不支持旧版.ppt格式: {file_path}")
                    return 0.2  # 至少10秒(1/6分钟)
            
            # Word文档
            elif ext in ['.docx', '.doc']:
                try:
                    try:
                        word = win32com.client.Dispatch("Word.Application")
                        word.Visible = False
                        doc = word.Documents.Open(file_path)
                        pages = doc.ComputeStatistics(2)  # 2代表页数
                        doc.Close()
                        word.Quit()
                        return round(max(0.2, pages * 0.2), 2)
                    except:
                    # 如果win32com方法失败，回退到估算方法
                        if ext == '.docx':
                            doc = docx.Document(file_path)
                            # 估算页数 (每15个段落约1页)
                            non_empty_paragraphs = [p for p in doc.paragraphs if p.text.strip()]
                            pages = max(1, len(non_empty_paragraphs) / 15)
                        else:  # .doc文件
                            # 使用文件大小估算
                            file_size = os.path.getsize(file_path)
                            pages = max(1, file_size / 10240)  # 假设每10KB约等于1页
                        return round(max(0.2, pages * 0.2), 2)
                except:
                    print(f"警告: 无法读取Word文档页数: {file_path}")
                    return 0.2              
                    
            # PDF文件
            elif ext == '.pdf':
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    pages = len(pdf_reader.pages)
                    return round(max(0.2, pages * 0.2), 2)
            
            # 其他文件类型，默认12秒
            else:
                print(f"未知文件类型: {ext}, 文件: {file_path}")
                return 0.2
                
        except Exception as e:
            print(f"读取文件出错 {file_path}: {str(e)}")
            return 0.2  # 出错时默认为12秒
    
    def check_existing_inventory(self, folder_path):
        """检查是否已有台账文件"""
        try:
            # 尝试从文件夹名称提取年份
            folder_name = os.path.basename(folder_path)
            year_match = re.search(r'(\d{4})年', folder_name)
            
            if year_match:
                year = year_match.group(1)
            else:
                # 如果无法从文件夹名提取年份，使用当前年份
                year = str(datetime.datetime.now().year)
                
            inventory_file = os.path.join(folder_path, f'{year}年党建安全工作台账.xlsx')
            
            if os.path.exists(inventory_file):
                self.status_var.set(f"发现现有台账文件: {os.path.basename(inventory_file)}")
            else:
                self.status_var.set("未发现现有台账文件，将创建新台账")
        except Exception as e:
            self.status_var.set(f"检查台账时出错: {str(e)}")
    
    def get_file_category(self, file_path):
        """确定文件类别"""
        path_parts = str(file_path).lower().split(os.sep)
        
        # 检查直接父文件夹
        parent_folder = path_parts[-2] if len(path_parts) > 1 else ""
        
        # 检查是否在安全学习文件夹下
        if "安全学习" in parent_folder:
            return "安全学习"
        # 检查是否在党建学习文件夹下
        elif "党建学习" in parent_folder:
            return "党建学习"
        # 检查是否包含关键词"安全"
        elif "安全" in parent_folder:
            return "安全学习"
        # 检查是否包含关键词"党建"
        elif "党建" in parent_folder:
            return "党建学习"
        
        # 检查是否在月份安全学习记录下但没有子文件夹
        month_pattern = re.compile(r'\d+月份安全学习记录')
        if any(month_pattern.search(part) for part in path_parts):
            # 检查是否有安全学习或党建学习子文件夹
            parent_dir = os.path.dirname(file_path)
            has_subfolders = False
            for item in os.listdir(parent_dir):
                if os.path.isdir(os.path.join(parent_dir, item)):
                    if "安全" in item or "党建" in item:
                        has_subfolders = True
                        break
            
            if not has_subfolders:
                return "安全学习"
        
        # 默认为安全工作
        return "安全工作"
    
    def generate_inventory(self):
        """生成文件台账"""
        safety_work_path = self.path_var.get()
        
        if not safety_work_path:
            messagebox.showerror("错误", "请选择年度安全工作文件夹")
            return
        
        if not os.path.exists(safety_work_path):
            messagebox.showerror("错误", "所选路径不存在，请重新选择")
            return
        
        # 尝试从文件夹名称提取年份
        folder_name = os.path.basename(safety_work_path)
        year_match = re.search(r'(\d{4})年', folder_name)
        
        if year_match:
            year = year_match.group(1)
        else:
            # 如果无法从文件夹名提取年份，使用当前年份
            year = str(datetime.datetime.now().year)
        
        # 设置输出文件路径（在所选文件夹下）
        output_path = os.path.join(safety_work_path, f'{year}年党建安全工作台账.xlsx')
        
        # 检查是否已有台账文件
        existing_data = None
        if os.path.exists(output_path):
            try:
                existing_data = pd.read_excel(output_path)
                # 创建备份
                if self.backup_var.get():
                    backup_time = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
                    backup_path = os.path.join(safety_work_path, f'{year}年党建安全工作台账_备份_{backup_time}.xlsx')
                    shutil.copy2(output_path, backup_path)
                    self.status_var.set(f"已创建现有台账的备份")
                    self.root.update()
            except Exception as e:
                messagebox.showwarning("警告", f"读取现有台账时出错: {str(e)}\n将创建新台账。")
                existing_data = None
        
        self.status_var.set(f"正在处理{year}年安全工作数据...")
        self.root.update()
        
        # 收集文件信息
        data = []
        count = 1
        
        try:
            # 遍历安全工作文件夹下的所有内容
            for root, dirs, files in os.walk(safety_work_path):
                for file in files:
                   # 跳过台账文件本身、备份文件和.db文件
                    if ("党建安全工作台账" in file and file.endswith(".xlsx")) or file.endswith(".db"):
                        continue
                    
                    # 可以根据需要添加更多要排除的文件类型
                    excluded_extensions = ['.db', '.tmp', '.ini', '.lnk', '.DS_Store', 'Thumbs.db']
                    if any(file.lower().endswith(ext) for ext in excluded_extensions):
                        continue

                    file_path = os.path.join(root, file)

                    # 获取文件名
                    file_name = os.path.splitext(file)[0]
                    
                    # 获取文件修改时间
                    mod_time = os.path.getmtime(file_path)
                    mod_date = datetime.datetime.fromtimestamp(mod_time).date()
                    
                    # 获取文件类别
                    category = self.get_file_category(file_path)
                    
                    # 创建文件超链接
                    file_link = f'=HYPERLINK("{file_path}", "打开文件")'
                    
                    # 计算培训时长
                    training_duration = self.get_training_duration(file_path, file_name)

                    # 添加到数据列表
                    data.append({
                        '序号': count,
                        '文件名': file_name,
                        '文件类别': category,
                        "培训时长（分钟）": training_duration,
                        '文件超链接': file_link,
                        '文件日期': mod_date
                    })
                    
                    count += 1
                    
                    # 更新状态
                    if count % 10 == 0:
                        self.status_var.set(f"已处理 {count-1} 个文件...")
                        self.root.update()
            
            # 创建DataFrame
            if data:
                new_df = pd.DataFrame(data)
                
                # 比较新旧数据
                if existing_data is not None:
                    # 提取文件路径进行比较
                    old_files = set()
                    new_files = set()
                    
                    # 从超链接中提取文件路径
                    def extract_path(hyperlink):
                        if isinstance(hyperlink, str) and hyperlink.startswith('=HYPERLINK('):
                            match = re.search(r'=HYPERLINK\("([^"]+)"', hyperlink)
                            if match:
                                return match.group(1)
                        return None
                    
                    for _, row in existing_data.iterrows():
                        if '文件超链接' in row:
                            path = extract_path(row['文件超链接'])
                            if path:
                                old_files.add(path)
                    
                    for _, row in new_df.iterrows():
                        if '文件超链接' in row:
                            path = extract_path(row['文件超链接'])
                            if path:
                                new_files.add(path)
                    
                    # 计算增减
                    added = len(new_files - old_files)
                    removed = len(old_files - new_files)
                    
                    # 保存新台账
                    new_df.to_excel(output_path, index=False)

                    self.adjust_column_widths(output_path, special_column='B')
                        
                    # 显示比较结果
                    message = f"台账已更新:\n- 新增文件: {added} 个\n- 移除文件: {removed} 个\n- 当前总文件数: {len(new_df)} 个"
                    self.status_var.set(f"台账已更新，新增 {added} 个文件，移除 {removed} 个文件")
                    messagebox.showinfo("更新完成", message)
                else:
                    # 保存新台账
                    new_df.to_excel(output_path, index=False)

                    if self.adjust_column_widths(output_path, special_column='B'):
                        self.status_var.set(f"台账生成完成，共 {len(new_df)} 个文件")
                        messagebox.showinfo("成功", f"台账已生成: {output_path}\n共包含 {len(new_df)} 个文件")
            else:
                self.status_var.set("未找到文件")
                messagebox.showwarning("警告", f"在所选文件夹中没有找到文件")
        
        except Exception as e:
            self.status_var.set("发生错误")
            messagebox.showerror("错误", f"生成台账时发生错误: {str(e)}")

if __name__ == "__main__":
    try:
        import pandas as pd
    except ImportError:
        tk.Tk().withdraw()
        messagebox.showerror("错误", "缺少必要的库。请安装pandas: pip install pandas")
        exit(1)
        
    root = tk.Tk()
    app = FileInventoryApp(root)
    root.mainloop()
