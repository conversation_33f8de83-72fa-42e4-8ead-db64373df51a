# -*- coding: utf-8 -*-
"""
Created on Mon Jan 27 10:12:39 2025

@author: <PERSON>
"""

import os
import json
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from openpyxl import Workbook, load_workbook

class MonitorConfig:
    """监控任务配置类"""
    def __init__(self, config_path):
        self.load_config(config_path)
        self.snapshot_file = f"{self.excel_path}.snapshot.json"
        self.last_snapshot = self._load_snapshot()

    def load_config(self, config_path):
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        self.root_path = Path(config["root_path"])
        self.excel_path = Path(config["excel_path"])
        self.sheet_rules = config["sheet_rules"]
        self.tag_rules = config["tag_rules"]
        self.link_type = config.get("link_type", "absolute")

    def _load_snapshot(self):
        """加载上次文件快照"""
        try:
            with open(self.snapshot_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}

class ExcelHandler:
    """Excel操作模块"""
    @staticmethod
    def update_sheet(wb, sheet_name, file_list):
        """创建/更新指定工作表"""
        if sheet_name not in wb.sheetnames:
            wb.create_sheet(sheet_name)
        
        ws = wb[sheet_name]
        ws.delete_rows(2, ws.max_row)  # 保留表头
        
        for row, file_info in enumerate(file_list, 2):
            ws[f"A{row}"] = file_info["name"]
            ws[f"B{row}"] = file_info["tag"]
            ws[f"C{row}"].hyperlink = file_info["path"]
            ws[f"C{row}"].value = "打开文件"

class FileScanner:
    """文件扫描引擎"""
    def __init__(self, config):
        self.config = config
    
    def get_current_state(self):
        """生成当前文件树快照"""
        file_tree = {}
        
        for sheet_rule in self.config.sheet_rules:
            sheet_name = sheet_rule["sheet_name"]
            scan_path = self.config.root_path / sheet_rule["folder_pattern"]
            
            file_tree[sheet_name] = []
            for file_path in scan_path.rglob("*"):
                if file_path.is_file():
                    file_tree[sheet_name].append({
                        "path": str(file_path),
                        "tag": self._get_file_tag(file_path)
                    })
        return file_tree
    
    def _get_file_tag(self, file_path):
        """根据规则生成文件标签"""
        relative_path = file_path.relative_to(self.config.root_path)
        for rule in self.config.tag_rules:
            if rule["depth"] == len(relative_path.parts) - 1:
                return rule["tag_name"]
        return "未分类"

class FileMonitor(FileSystemEventHandler):
    """增强型文件监控"""
    def __init__(self, config):
        self.config = config
        self.scanner = FileScanner(config)
        self.excel_handler = ExcelHandler()

    def on_modified(self, event):
        """处理文件变更"""
        current_state = self.scanner.get_current_state()
        self._sync_excel(current_state)
    
    def on_deleted(self, event):
        """处理文件删除"""
        if not event.is_directory:
            confirm = input(f"检测到文件被删除：{event.src_path}，是否移除台账记录？[y/n]")
            if confirm.lower() == 'y':
                self._remove_from_excel(event.src_path)

    def _sync_excel(self, current_state):
        """同步Excel台账"""
        try:
            wb = load_workbook(self.config.excel_path)
        except FileNotFoundError:
            wb = Workbook()
            wb.remove(wb.active)
        
        for sheet_name, files in current_state.items():
            file_list = [{
                "name": Path(f["path"]).name,
                "tag": f["tag"],
                "path": f["path"]
            } for f in files]
            
            self.excel_handler.update_sheet(wb, sheet_name, file_list)
        
        wb.save(self.config.excel_path)
        self._save_snapshot(current_state)

    def _remove_from_excel(self, file_path):
        """从台账删除记录"""
        # 实现跨Sheet的删除逻辑（需要匹配文件路径）
        pass

    def _save_snapshot(self, current_state):
        """保存当前状态快照"""
        with open(self.config.snapshot_file, 'w') as f:
            json.dump(current_state, f)

def start_monitoring(config_path):
    """启动监控任务"""
    config = MonitorConfig(config_path)
    event_handler = FileMonitor(config)
    
    observer = Observer()
    observer.schedule(event_handler, str(config.root_path), recursive=True)
    observer.start()
    
    try:
        while True:
            time.sleep(5)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()